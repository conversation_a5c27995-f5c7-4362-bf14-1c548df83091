@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Ensure form elements inherit proper text colors */
input, textarea, select {
  color: inherit;
}

/* Override for specific form styling to ensure visibility */
.form-input, .form-textarea {
  color: #1f2937 !important; /* gray-800 */
  background-color: #ffffff !important;
}

/* Defensive styling for all textareas to ensure text is always visible */
textarea {
  color: #1f2937 !important; /* Ensure dark text on light background */
  background-color: #ffffff !important;
}

@media (prefers-color-scheme: dark) {
  .form-input, .form-textarea {
    color: #f9fafb !important; /* gray-50 */
    background-color: #374151 !important; /* gray-700 */
  }

  textarea {
    color: #f9fafb !important; /* Light text on dark background */
    background-color: #374151 !important; /* gray-700 */
  }
}
