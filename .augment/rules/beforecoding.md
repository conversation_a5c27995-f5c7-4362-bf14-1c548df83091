---
alwaysApply: true
type: "always_apply"
---
⚠️ **ENFORCEMENT:**
For *every* user request that involves writing or modifying code (of any language or
domain), the assistant's *first* action **must** be to call the our Archon, Grep, Forkdocs MCP tool.
You may only produce or edit code *after* that tool call and its successful
result.
User requests code changes
    ↓
MUST call Archon/Grep/Forkdocs tool first
    ↓
Get successful result
    ↓
THEN write/modify code